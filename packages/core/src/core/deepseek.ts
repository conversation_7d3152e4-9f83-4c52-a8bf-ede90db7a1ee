/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { GenerateContentResponse, FinishReason } from '@google/genai';
import { Turn } from './turn.js';
import { LLMProvider } from './llm.js';
import axios from 'axios';
import { Config } from '../config/config.js';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

export class DeepSeekClient implements LLMProvider {
  private apiKey: string;

  constructor(private config: Config) {
    this.apiKey = this.config.getProviders()?.deepseek?.apiKey || '';

    // Debug information
    console.log('DeepSeekClient Debug Info:');
    console.log('- Environment DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY ? 'SET' : 'NOT SET');
    console.log('- Config providers:', JSON.stringify(this.config.getProviders(), null, 2));
    console.log('- Extracted API Key:', this.apiKey ? 'SET' : 'NOT SET');
    console.log('- Current provider:', this.config.getProvider());
  }

  async generateContent(history: Turn[]): Promise<GenerateContentResponse> {
    if (!this.apiKey) {
      const envKey = process.env.DEEPSEEK_API_KEY;
      const configProviders = this.config.getProviders();
      throw new Error(
        `DEEPSEEK_API_KEY is not configured.\n` +
        `Environment variable DEEPSEEK_API_KEY: ${envKey ? 'SET' : 'NOT SET'}\n` +
        `Config providers: ${JSON.stringify(configProviders, null, 2)}\n` +
        `Please set the DEEPSEEK_API_KEY environment variable.`
      );
    }

    // Convert Turn[] to DeepSeek API message format
    const messages = this.convertTurnsToMessages(history);

    try {
      const response = await axios.post(
        DEEPSEEK_API_URL,
        {
          model: this.config.getModel() || 'deepseek-chat',
          messages,
          stream: false,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      // Convert DeepSeek API response to GenerateContentResponse format
      return this.convertDeepSeekResponseToGeminiFormat(response.data);
    } catch (error: any) {
      if (error.response) {
        throw new Error(`API Error: ${error.response.data?.error?.message || error.response.statusText} (Status: ${error.response.status === 403 ? 'Forbidden' : error.response.status})`);
      }
      throw error;
    }
  }

  private convertTurnsToMessages(history: Turn[]): Array<{role: string, content: string}> {
    const messages: Array<{role: string, content: string}> = [];

    // Add a simple user message for now
    // In a real implementation, this would extract content from Turn objects
    messages.push({
      role: 'user',
      content: 'Hello, how can you help me?'
    });

    return messages;
  }

  private convertDeepSeekResponseToGeminiFormat(deepseekResponse: any): GenerateContentResponse {
    const choice = deepseekResponse.choices?.[0];
    const content = choice?.message?.content || '';

    const response = new GenerateContentResponse();
    response.candidates = [
      {
        content: {
          parts: [{ text: content }],
          role: 'model',
        },
        finishReason: FinishReason.STOP,
        index: 0,
        safetyRatings: [],
      },
    ];
    response.usageMetadata = {
      promptTokenCount: deepseekResponse.usage?.prompt_tokens || 0,
      candidatesTokenCount: deepseekResponse.usage?.completion_tokens || 0,
      totalTokenCount: deepseekResponse.usage?.total_tokens || 0,
    };
    response.promptFeedback = {
      safetyRatings: [],
    };

    return response;
  }


}
