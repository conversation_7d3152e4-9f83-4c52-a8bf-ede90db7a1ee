/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Config } from '../config/config.js';
import { DeepSeekClient } from './deepseek.js';
import { GeminiClient } from './client.js';
import { <PERSON><PERSON>rovider } from './llm.js';
import { AuthType, ContentGeneratorConfig } from '../config/auth.js';

export async function createContentGeneratorConfig(
  model: string,
  authType: AuthType,
  config: Config,
): Promise<ContentGeneratorConfig> {
  return {
    model,
    authType,
  };
}

/**
 * Interface abstracting the core functionalities for generating content and counting tokens.
 */
export async function createContentGenerator(
  config: Config,
): Promise<LLMProvider> {
  const provider = config.getProvider();

  switch (provider) {
    case 'google':
      return new GeminiClient(config);
    case 'deepseek':
      return new DeepSeekClient(config);
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}
