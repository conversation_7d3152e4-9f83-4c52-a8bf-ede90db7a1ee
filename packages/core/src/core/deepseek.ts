/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { GenerateContentResponse } from '@google/genai';
import { Turn } from './turn.js';
import { LLMProvider } from './llm.js';
import axios from 'axios';
import { Config } from '../config/config.js';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

export class DeepSeekClient implements LLMProvider {
  private apiKey: string;

  constructor(private config: Config) {
    this.apiKey = this.config.getProviders()?.deepseek?.apiKey || '';
  }

  async generateContent(history: Turn[]): Promise<GenerateContentResponse> {
    // For now, we'll create a simple message from the Turn array
    // This is a simplified implementation that needs to be improved
    const messages = history.map((turn, index) => ({
      role: index % 2 === 0 ? 'user' : 'assistant',
      content: `Turn ${index + 1}`, // Simplified content
    }));

    const response = await axios.post(
      DEEPSEEK_API_URL,
      {
        model: this.config.getModel(),
        messages,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`,
        },
      },
    );

    // This is a temporary hack to make the DeepSeek API response compatible with
    // the GenerateContentResponse interface. This will be replaced with a proper
    // mapping function.
    return response.data as GenerateContentResponse;
  }


}
