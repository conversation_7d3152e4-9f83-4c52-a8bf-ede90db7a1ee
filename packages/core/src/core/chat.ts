/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { PartListUnion } from '@google/genai';
import { Turn, ServerGeminiStreamEvent, GeminiEventType } from './turn.js';
import { LLMProvider } from './llm.js';

export class Chat {
  private readonly MAX_TURNS = 100;
  private history: Turn[] = [];

  constructor(private llmProvider: LLMProvider) {}

  async *sendMessageStream(
    request: PartListUnion,
    signal: AbortSignal,
    turns: number = this.MAX_TURNS,
  ): AsyncGenerator<ServerGeminiStreamEvent, Turn> {
    if (!turns) {
      const turn = new Turn(this.history);
      this.history.push(turn);
      return turn;
    }

    const turn = new Turn(this.history);
    this.history.push(turn);
    const resultStream = this.llmProvider.generateContent(this.history);
    // This is a hack to make the stream work. This will be replaced with a
    // proper implementation that streams the response from the LLM.
    const result = await resultStream;
    turn.addResponse(result);
    yield { type: GeminiEventType.ToolCall, value: turn.pendingToolCalls };

    if (!turn.pendingToolCalls.length && signal && !signal.aborted) {
      // TODO(zxnap): Re-enable next speaker check.
      // const nextSpeakerCheck = await checkNextSpeaker(
      //   this.getChat(),
      //   this,
      //   signal,
      // );
      // if (nextSpeakerCheck?.next_speaker === 'model') {
      //   const nextRequest = [{ text: 'Please continue.' }];
      //   // This recursive call's events will be yielded out, but the final
      //   // turn object will be from the top-level call.
      //   yield* this.sendMessageStream(nextRequest, signal, turns - 1);
      // }
    }
    return turn;
  }

  getHistory(): Turn[] {
    return this.history;
  }
}
